/**
 * @file main.cpp
 * @brief TCP消息总线客户端测试程序
 * <AUTHOR>
 * @date 2025-08-24
 */

#include <signal.h>
#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <functional>
#include <iostream>
#include <memory>
#include <string>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/base/message.hpp"

using namespace zexuan::net;
using namespace zexuan::bus;
using namespace zexuan::base;

// 全局变量
std::atomic<bool> g_running{true};
std::shared_ptr<TcpBusClient> g_client;
std::shared_ptr<EventLoop> g_event_loop;
std::atomic<bool> g_event_reporting{false};

// 前向声明
void handleReceivedMessage(TcpBusClient& client, const zexuan::base::CommonMessage& message);
void generateResponseMessage(TcpBusClient& client, const zexuan::base::CommonMessage& original_message);
void sendSingleResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message,
                       const zexuan::base::Message& input_msg);
void sendMultiFrameResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message,
                           const zexuan::base::Message& input_msg);
void sendSimpleResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message);
void startEventReporting();
void stopEventReporting();
void generateTimestampEvent();

// 信号处理函数
void signalHandler(int signal) {
    // 在信号处理函数中只设置标志，不直接操作复杂对象
    g_running.store(false);

    // 让事件循环退出
    if (g_event_loop) {
        g_event_loop->quit();
    }
}

// CommonMessage回调
void onCommonMessage(const zexuan::base::CommonMessage& msg) {
    spdlog::info("Received CommonMessage: type={}, source_id={}, target_id={}, invoke_id={}, data_size={}",
                static_cast<int>(msg.type), msg.source_id, msg.target_id, msg.invoke_id, msg.data.size());

    if (g_client) {
        handleReceivedMessage(*g_client, msg);
    }
}

// EventMessage回调
void onEventMessage(const zexuan::base::EventMessage& msg) {
    spdlog::info("Received EventMessage: event_type={}, device_id={}, source_id={}, description={}, data_size={}",
                msg.event_type, msg.device_uuid.device_id, msg.source_id, msg.description, msg.data.size());

    // EventMessage暂时不处理，只记录
    spdlog::debug("EventMessage received but not processed");
}

// ControlMessage回调
void onControlMessage(const ControlMessage& msg) {
    spdlog::info("Received ControlMessage: action={}, success={}, message={}", 
                msg.action, msg.success, msg.message);
    
    if (msg.action == "subscribe_response") {
        if (msg.success) {
            spdlog::info("Subscription successful. Subscribed message types: {}, event types: {}", 
                        msg.subscribed_message_types.size(), msg.subscribed_event_types.size());
        } else {
            spdlog::error("Subscription failed: {}", msg.message);
        }
    }
}

// 处理接收到的消息并生成响应
void handleReceivedMessage(TcpBusClient& client, const zexuan::base::CommonMessage& message) {
    spdlog::info("Processing CommonMessage: type={}, source_id={}, invoke_id={}",
                static_cast<int>(message.type), message.source_id, message.invoke_id);

    // 只处理来自ProtocolService的消息（避免处理自己发送的消息）
    if (message.source_id == zexuan::base::SERVICE_SUBJECT_ID) {  // SERVICE_SUBJECT_ID = 2050
        // 尝试解析为IEC103消息并生成响应
        generateResponseMessage(client, message);
    } else {
        spdlog::debug("Ignoring message not from ProtocolService (source_id={}, expected={})", 
                     message.source_id, zexuan::base::SERVICE_SUBJECT_ID);
    }
}

// 生成响应消息（移植自ProtocolService::ProcessBusinessMessage）
void generateResponseMessage(TcpBusClient& client, const zexuan::base::CommonMessage& original_message) {
    spdlog::debug("Processing business message: invoke_id={}", original_message.invoke_id);

    // 尝试解析输入的IEC103消息
    zexuan::base::Message input_msg;
    size_t parsed = input_msg.deserialize(original_message.data);

    if (parsed > 0) {
        // 成功解析为IEC103消息
        spdlog::debug("Parsed input IEC103 message: TYP={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                     input_msg.getTyp(), input_msg.getVsq(), input_msg.getCot(), input_msg.getFun(),
                     input_msg.getInf());

        // 根据 Type 决定发送单帧还是多帧响应
        if (input_msg.getTyp() == 1) {
            // Type 1: 单帧测试，发送一个响应
            sendSingleResponse(client, original_message, input_msg);
        } else if (input_msg.getTyp() == 2) {
            // Type 2: 多帧测试，发送两个响应
            sendMultiFrameResponse(client, original_message, input_msg);
        } else {
            // 其他类型，默认单帧响应
            sendSingleResponse(client, original_message, input_msg);
        }
    } else {
        // 不能解析为IEC103，生成简单响应
        sendSimpleResponse(client, original_message);
    }
}

// 发送单帧响应
void sendSingleResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message,
                       const zexuan::base::Message& input_msg) {
    spdlog::debug("Sending single response for Type 1");

    zexuan::base::CommonMessage response;
    response.type = zexuan::base::MessageType::RESULT;  // 设置为RESULT类型
    response.source_id = client.getClientId();          // 当前客户端ID作为source_id
    response.target_id = original_message.source_id;   // 原始发送者ID作为target_id
    response.invoke_id = original_message.invoke_id;
    response.b_lastmsg = true;  // 单帧响应，肯定是最后一帧

    // 创建响应消息
    zexuan::base::Message response_msg;
    response_msg.setTyp(input_msg.getTyp());
    response_msg.setVsq(input_msg.getVsq());
    response_msg.setCot(0x07);  // 激活确认
    response_msg.setSource(input_msg.getTarget());
    response_msg.setTarget(input_msg.getSource());
    response_msg.setFun(input_msg.getFun());
    response_msg.setInf(input_msg.getInf());

    // 设置响应内容
    std::string response_content = "SINGLE_RESPONSE_TO_" + input_msg.getTextContent();
    response_msg.setTextContent(response_content);

    // 序列化响应消息
    std::vector<uint8_t> serialized_response;
    size_t size = response_msg.serialize(serialized_response);

    if (size > 0) {
        response.data = serialized_response;
        spdlog::debug("Generated single IEC103 response message ({} bytes)", size);
    } else {
        // 序列化失败，使用简单响应
        std::string simple_response = "SINGLE_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());
    }

    // 发送响应到总线
    if (client.sendCommonMessage(response)) {
        spdlog::debug("Sent single response to bus: invoke_id={}", response.invoke_id);
    } else {
        spdlog::error("Failed to send single response to bus: invoke_id={}", response.invoke_id);
    }
}

// 发送多帧响应
void sendMultiFrameResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message,
                           const zexuan::base::Message& input_msg) {
    spdlog::debug("Sending multi-frame response for Type 2");

    // 发送第一帧
    zexuan::base::CommonMessage response1;
    response1.type = zexuan::base::MessageType::RESULT;  // 设置为RESULT类型
    response1.source_id = client.getClientId();          // 当前客户端ID作为source_id
    response1.target_id = original_message.source_id;    // 原始发送者ID作为target_id
    response1.invoke_id = original_message.invoke_id;
    response1.b_lastmsg = false;  // 不是最后一帧

    // 创建第一个响应消息
    zexuan::base::Message response_msg1;
    response_msg1.setTyp(input_msg.getTyp());
    response_msg1.setVsq(input_msg.getVsq());
    response_msg1.setCot(0x07);  // 激活确认
    response_msg1.setSource(input_msg.getTarget());
    response_msg1.setTarget(input_msg.getSource());
    response_msg1.setFun(input_msg.getFun());
    response_msg1.setInf(input_msg.getInf());

    std::string response_content1 = "MULTI_RESPONSE_1_TO_" + input_msg.getTextContent();
    response_msg1.setTextContent(response_content1);

    std::vector<uint8_t> serialized_response1;
    size_t size1 = response_msg1.serialize(serialized_response1);

    if (size1 > 0) {
        response1.data = serialized_response1;
    } else {
        std::string simple_response1 = "MULTI_RESPONSE_1_FOR_" + original_message.invoke_id;
        response1.data.assign(simple_response1.begin(), simple_response1.end());
    }

    // 发送第一帧
    if (client.sendCommonMessage(response1)) {
        spdlog::debug("Sent first frame of multi-response to bus: invoke_id={}", response1.invoke_id);
    } else {
        spdlog::error("Failed to send first frame of multi-response to bus: invoke_id={}", response1.invoke_id);
        return;
    }

    // 使用定时器延迟发送第二帧
    if (g_event_loop) {
        g_event_loop->runAfter(0.1, [&client, original_message, input_msg]() {
            // 发送第二帧
            zexuan::base::CommonMessage response2;
    response2.type = zexuan::base::MessageType::RESULT;  // 设置为RESULT类型
    response2.source_id = client.getClientId();          // 当前客户端ID作为source_id
    response2.target_id = original_message.source_id;    // 原始发送者ID作为target_id
    response2.invoke_id = original_message.invoke_id;
    response2.b_lastmsg = true;  // 这是最后一帧

    // 创建第二个响应消息
    zexuan::base::Message response_msg2;
    response_msg2.setTyp(input_msg.getTyp());
    response_msg2.setVsq(input_msg.getVsq());
    response_msg2.setCot(0x0A);  // 激活终止
    response_msg2.setSource(input_msg.getTarget());
    response_msg2.setTarget(input_msg.getSource());
    response_msg2.setFun(input_msg.getFun());
    response_msg2.setInf(input_msg.getInf());

    std::string response_content2 = "MULTI_RESPONSE_2_TO_" + input_msg.getTextContent();
    response_msg2.setTextContent(response_content2);

    std::vector<uint8_t> serialized_response2;
    size_t size2 = response_msg2.serialize(serialized_response2);

    if (size2 > 0) {
        response2.data = serialized_response2;
    } else {
        std::string simple_response2 = "MULTI_RESPONSE_2_FOR_" + original_message.invoke_id;
        response2.data.assign(simple_response2.begin(), simple_response2.end());
    }

            // 发送第二帧
            if (client.sendCommonMessage(response2)) {
                spdlog::debug("Sent second frame of multi-response to bus: invoke_id={}", response2.invoke_id);
            } else {
                spdlog::error("Failed to send second frame of multi-response to bus: invoke_id={}", response2.invoke_id);
            }
        });
    }
}

// 发送简单响应
void sendSimpleResponse(TcpBusClient& client, const zexuan::base::CommonMessage& original_message) {
    spdlog::debug("Sending simple response for non-IEC103 message");

    zexuan::base::CommonMessage response;
    response.type = zexuan::base::MessageType::RESULT;  // 设置为RESULT类型
    response.source_id = client.getClientId();          // 当前客户端ID作为source_id
    response.target_id = original_message.source_id;    // 原始发送者ID作为target_id
    response.invoke_id = original_message.invoke_id;
    response.b_lastmsg = true;  // 简单响应，肯定是最后一帧

    // 生成简单响应
    std::string simple_response = "SIMPLE_RESPONSE_FOR_" + original_message.invoke_id;
    response.data.assign(simple_response.begin(), simple_response.end());

    // 发送响应到总线
    if (client.sendCommonMessage(response)) {
        spdlog::debug("Sent simple response to bus: invoke_id={}", response.invoke_id);
    } else {
        spdlog::error("Failed to send simple response to bus: invoke_id={}", response.invoke_id);
    }
}

int main(int argc, char* argv[]) {
    // 解析命令行参数
    std::string server_host = "127.0.0.1";
    uint16_t server_port = 8081;
    std::string client_name = "TestClient";
    std::string config_path = "./config/config.json";
    
    if (argc > 1) {
        server_host = argv[1];
    }
    if (argc > 2) {
        server_port = static_cast<uint16_t>(std::stoi(argv[2]));
    }
    if (argc > 3) {
        client_name = argv[3];
    }
    if (argc > 4) {
        config_path = argv[4];
    }
    
    // 初始化日志系统
    if (!LoggerManager::initialize("tcp_bus_client.log", config_path)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return -1;
    }
    
    spdlog::info("TCP Bus Client starting...");
    spdlog::info("Server: {}:{}, Client: {}", server_host, server_port, client_name);
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 创建事件循环
        g_event_loop = std::make_shared<EventLoop>();

        // 创建TCP客户端
        g_client = std::make_shared<TcpBusClient>(g_event_loop.get(), server_host, server_port, client_name);
        
        // 设置客户端ID（用于点对点通信）
        g_client->setClientId(9);  // 设置为9，与client发送的target匹配
        
        // 设置回调函数
        g_client->setCommonMessageCallback(onCommonMessage);
        g_client->setEventMessageCallback(onEventMessage);
        g_client->setControlMessageCallback(onControlMessage);
        
        // 启动连接（异步，自动重连）
        g_client->connect();
        spdlog::info("Connecting to server...");

        // 使用定时器检查连接状态并订阅
        std::function<void()> checkConnectionAndSubscribe = [&]() {
            if (!g_running.load()) {
                return;
            }

            if (!g_client->isConnected()) {
                // 每100ms检查一次连接状态
                g_event_loop->runAfter(0.1, checkConnectionAndSubscribe);
                return;
            }

            spdlog::info("Connected to server successfully");

            // 订阅所有消息类型和事件类型
            std::vector<int> message_types;
            std::vector<int> event_types;

            // 订阅所有CommonMessage类型 (假设类型1-10)
            for (int i = 1; i <= 10; ++i) {
                message_types.push_back(i);
            }

            // 订阅统一的事件类型1
            event_types.push_back(1);

            spdlog::info("About to call subscribe with {} message types and {} event types",
                        message_types.size(), event_types.size());

            bool subscribe_result = g_client->subscribe(message_types, event_types);
            spdlog::info("Subscribe result: {}", subscribe_result);

            if (subscribe_result) {
                spdlog::info("Sent subscription request for all message types");

                // 启动定时事件上报
                spdlog::info("About to start event reporting...");
                startEventReporting();
                spdlog::info("Started automatic event reporting");
            } else {
                spdlog::error("Failed to send subscription request");
            }
        };

        // 启动连接检查，10秒后超时
        g_event_loop->runAfter(0.1, checkConnectionAndSubscribe);
        g_event_loop->runAfter(10.0, [&]() {
            if (!g_client->isConnected()) {
                spdlog::error("Failed to connect to server after 10 seconds");
                g_running.store(false);
                g_event_loop->quit();
            }
        });

        // 运行事件循环（阻塞直到quit()被调用）
        g_event_loop->loop();

        spdlog::info("Event loop stopped");

        // 停止事件上报
        stopEventReporting();

        // 确保完全清理客户端
        if (g_client) {
            g_client->disconnect();
            g_client.reset();
        }

        // 清理事件循环
        g_event_loop.reset();
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return -1;
    }
    
    spdlog::info("TCP Bus Client shutdown completed");
    return 0;
}

// 事件上报相关函数实现
void startEventReporting() {
    g_event_reporting.store(true);
    spdlog::info("Event report started using timer");

    // 使用定时器代替线程，每3秒生成一次时间戳事件
    if (g_event_loop) {
        g_event_loop->runEvery(3.0, []() {
            if (g_event_reporting.load()) {
                // generateTimestampEvent();
            }
        });
    }
}

void stopEventReporting() {
    g_event_reporting.store(false);
    spdlog::info("Event report stopped");
}



void generateTimestampEvent() {
    if (!g_client || !g_client->isConnected()) {
        return;
    }

    // 创建时间戳事件
    zexuan::base::EventMessage event_msg;
    event_msg.event_type = 1;  // 统一的事件类型
    event_msg.source_id = g_client->getClientId();  // 使用客户端ID
    event_msg.description = "Automatic timestamp event from tcp_bus_client";

    // 添加当前时间戳作为数据
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::string timestamp = std::to_string(time_t);
    event_msg.data.assign(timestamp.begin(), timestamp.end());

    // 发送事件到总线
    if (g_client->sendEventMessage(event_msg)) {
        spdlog::debug("Generated and sent timestamp event: {}", timestamp);
    } else {
        spdlog::error("Failed to send timestamp event");
    }
}
