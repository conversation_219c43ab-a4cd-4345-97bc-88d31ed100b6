/**
 * @file timer_queue.cpp
 * @brief 定时器队列实现
 * <AUTHOR>
 * @date 2025-08-25
 */

#include "zexuan/net/timer_queue.hpp"

#include <spdlog/spdlog.h>
#include <sys/timerfd.h>
#include <unistd.h>

#include <algorithm>
#include <cassert>
#include <cstring>

#include "zexuan/net/channel.hpp"
#include "zexuan/net/event_loop.hpp"

using namespace zexuan;
using namespace zexuan::net;

namespace {
  /**
   * @brief 创建 timerfd
   */
  int createTimerfd() {
    int timerfd = ::timerfd_create(CLOCK_MONOTONIC, TFD_NONBLOCK | TFD_CLOEXEC);
    if (timerfd < 0) {
      spdlog::critical("Failed to create timerfd: {}", strerror(errno));
      abort();
    }
    return timerfd;
  }

  /**
   * @brief 计算从现在到指定时间的时间差
   */
  struct timespec howMuchTimeFromNow(Timestamp when) {
    auto now = std::chrono::system_clock::now();
    auto duration = when - now;
    
    // 确保至少有100微秒的延迟
    auto microseconds = std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    if (microseconds < 100) {
      microseconds = 100;
    }

    struct timespec ts;
    ts.tv_sec = static_cast<time_t>(microseconds / 1000000);
    ts.tv_nsec = static_cast<long>((microseconds % 1000000) * 1000);
    return ts;
  }

  /**
   * @brief 读取 timerfd
   */
  void readTimerfd(int timerfd) {
    uint64_t howmany;
    ssize_t n = ::read(timerfd, &howmany, sizeof howmany);
    spdlog::trace("TimerQueue::handleRead() {} events", howmany);
    if (n != sizeof howmany) {
      spdlog::error("TimerQueue::handleRead() reads {} bytes instead of 8", n);
    }
  }

  /**
   * @brief 重置 timerfd 的到期时间
   */
  void resetTimerfd(int timerfd, std::chrono::system_clock::time_point expiration) {
    struct itimerspec newValue;
    struct itimerspec oldValue;
    std::memset(&newValue, 0, sizeof newValue);
    std::memset(&oldValue, 0, sizeof oldValue);
    
    newValue.it_value = howMuchTimeFromNow(expiration);
    int ret = ::timerfd_settime(timerfd, 0, &newValue, &oldValue);
    if (ret) {
      spdlog::error("timerfd_settime() failed: {}", strerror(errno));
    }
  }

}  // namespace

TimerQueue::TimerQueue(EventLoop* loop)
    : loop_(loop),
      timerfd_(createTimerfd()),
      timerfdChannel_(std::make_unique<Channel>(loop, timerfd_)),
      timers_(),
      callingExpiredTimers_(false) {
  timerfdChannel_->setReadCallback([this](Timestamp ts) { handleRead(); });
  // 始终监听 timerfd 的可读事件
  timerfdChannel_->enableReading();
}

TimerQueue::~TimerQueue() {
  timerfdChannel_->disableAll();
  timerfdChannel_->remove();
  ::close(timerfd_);
  
  // 清理所有定时器
  for (const Entry& timer : timers_) {
    // Timer 由 timerStorage_ 中的 unique_ptr 管理，这里不需要手动删除
  }
}

TimerId TimerQueue::addTimer(TimerCallback cb, Timestamp when, double interval) {
  auto timer = std::make_unique<Timer>(std::move(cb), when, interval);
  Timer* timerPtr = timer.get();
  TimerId timerId(timerPtr, timerPtr->sequence());
  
  loop_->runInLoop([this, timer = std::move(timer)]() mutable {
    addTimerInLoop(std::move(timer));
  });
  
  return timerId;
}

void TimerQueue::cancel(TimerId timerId) {
  loop_->runInLoop([this, timerId]() { cancelInLoop(timerId); });
}

void TimerQueue::addTimerInLoop(TimerPtr timer) {
  loop_->assertInLoopThread();
  
  Timer* timerPtr = timer.get();
  bool earliestChanged = insert(timerPtr);
  
  // 将 timer 存储到 timerStorage_ 中管理内存
  timerStorage_.push_back(std::move(timer));

  if (earliestChanged) {
    resetTimerfd(timerfd_, timerPtr->expiration());
  }
}

void TimerQueue::cancelInLoop(TimerId timerId) {
  loop_->assertInLoopThread();
  assert(timers_.size() == activeTimers_.size());
  
  ActiveTimer timer(timerId.timer_, timerId.sequence_);
  ActiveTimerSet::iterator it = activeTimers_.find(timer);
  
  if (it != activeTimers_.end()) {
    size_t n = timers_.erase(Entry(it->first->expiration(), it->first));
    assert(n == 1);
    (void)n;
    
    // 从 timerStorage_ 中移除对应的 unique_ptr
    auto storageIt = std::find_if(timerStorage_.begin(), timerStorage_.end(),
                                  [timerPtr = it->first](const TimerPtr& ptr) {
                                    return ptr.get() == timerPtr;
                                  });
    if (storageIt != timerStorage_.end()) {
      timerStorage_.erase(storageIt);
    }
    
    activeTimers_.erase(it);
  } else if (callingExpiredTimers_) {
    cancelingTimers_.insert(timer);
  }
  
  assert(timers_.size() == activeTimers_.size());
}

void TimerQueue::handleRead() {
  loop_->assertInLoopThread();
  Timestamp now = std::chrono::system_clock::now();
  readTimerfd(timerfd_);

  std::vector<Entry> expired = getExpired(now);

  callingExpiredTimers_ = true;
  cancelingTimers_.clear();

  // 安全地执行回调函数
  for (const Entry& it : expired) {
    it.second->run();
  }
  callingExpiredTimers_ = false;

  reset(expired, now);
}

std::vector<TimerQueue::Entry> TimerQueue::getExpired(Timestamp now) {
  assert(timers_.size() == activeTimers_.size());
  std::vector<Entry> expired;

  Entry sentry(now, reinterpret_cast<Timer*>(UINTPTR_MAX));
  TimerList::iterator end = timers_.lower_bound(sentry);
  assert(end == timers_.end() || now < end->first);

  std::copy(timers_.begin(), end, std::back_inserter(expired));
  timers_.erase(timers_.begin(), end);

  for (const Entry& it : expired) {
    ActiveTimer timer(it.second, it.second->sequence());
    size_t n = activeTimers_.erase(timer);
    assert(n == 1);
    (void)n;
  }

  assert(timers_.size() == activeTimers_.size());
  return expired;
}

void TimerQueue::reset(const std::vector<Entry>& expired, Timestamp now) {
  Timestamp nextExpire;

  for (const Entry& it : expired) {
    ActiveTimer timer(it.second, it.second->sequence());
    if (it.second->repeat() && cancelingTimers_.find(timer) == cancelingTimers_.end()) {
      it.second->restart(now);
      insert(it.second);
    } else {
      // 从 timerStorage_ 中移除对应的 unique_ptr
      auto storageIt = std::find_if(timerStorage_.begin(), timerStorage_.end(),
                                    [timerPtr = it.second](const TimerPtr& ptr) {
                                      return ptr.get() == timerPtr;
                                    });
      if (storageIt != timerStorage_.end()) {
        timerStorage_.erase(storageIt);
      }
    }
  }

  if (!timers_.empty()) {
    nextExpire = timers_.begin()->second->expiration();
  }

  if (nextExpire != Timestamp{}) {
    resetTimerfd(timerfd_, nextExpire);
  }
}

bool TimerQueue::insert(Timer* timer) {
  loop_->assertInLoopThread();
  assert(timers_.size() == activeTimers_.size());

  bool earliestChanged = false;
  Timestamp when = timer->expiration();
  TimerList::iterator it = timers_.begin();

  if (it == timers_.end() || when < it->first) {
    earliestChanged = true;
  }

  {
    std::pair<TimerList::iterator, bool> result = timers_.insert(Entry(when, timer));
    assert(result.second);
    (void)result;
  }

  {
    std::pair<ActiveTimerSet::iterator, bool> result =
        activeTimers_.insert(ActiveTimer(timer, timer->sequence()));
    assert(result.second);
    (void)result;
  }

  assert(timers_.size() == activeTimers_.size());
  return earliestChanged;
}
