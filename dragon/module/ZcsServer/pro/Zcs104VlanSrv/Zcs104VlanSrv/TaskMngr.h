


#ifndef TASKMNGR_H
#define TASKMNGR_H

#include <sys/syscall.h>
#include <vector>

#include "ZxGlobal_Def.h"
#include "ZxMessageLog.h"
#include "ZxTime.h"
#include "ZclLibmngr_Gbk2Utf8.h"
#include "ZxSttpDefine.h"
#include "ZxLoadBusSwapLib.h"
#include "push.h"
#include "push103.h"

// 服务器在线管理器相关头文件
#include "../../common/ZxSrvOnlineManagerWrapper.h"
#include "../../../../libapimngr/ZxLibApi_SrvOnlineMngr.h"

//#include "ZxAPCIInterFace.h"
//#include "ZxMsgCaster.h"
#include "Zx104APCIHandler.h"
#include "ZxGB103MsgAttachFactory.h"
#include "ZxPro103ClientWay.h"
#include "ZxHuNTc103ASDUHandler.h"

//程序名 用来注册总线和日志目录用
#define  EXE_NAME "Zcs104VlanSrv"
#define  LISTEN_DO_THREAD_NUM  1
//程序配置文件
#define  Zcs104VlanSrvINI "./Zcs104VlanSrv.ini"
#define  Ch2PtIdINI "./Ch2PtId.ini"
//获取工作区用，和老宋的前置程序要一致，不然找不到init_大安_？.xml文件名
#define  ZAS_BUS_DEF_CONFIG_INI "../lib/bus_def.ini"
//服务器在线管理器配置文件 - 使用项目标准配置文件路径
#define  ZCS_SERVER_INI "../../server/ZcsServer.ini"


#define  CYCLETIME  120
//UTF82BGK用
#define  MAX_CHAR_BUFF_LEN 1000 
#define MAXFILENAME (256)
#define size_buf 1638400
#define WRITEBUFFERSIZE 16384

typedef struct _LOG_INFO
{
	string          strLogPath;	//日志总根路径
	int		nLogLevel;	//日志级别
	int		nLogRrdDay;	//日志默认天数
	int		nLogMaxSize;	//日志文件默认大小(单位:Kb)
        bool            bShowDebug;//true-生成调试日志 false-不生成调试日志
	_LOG_INFO()
	{
		strLogPath = "../zx_log";
		nLogLevel	= 3;
		nLogRrdDay	= 3;
		nLogMaxSize = 1024*1024;
                bShowDebug = false;
	}
}LOG_INFO;
typedef struct _LISTEN_CFG 
{
        map<string,string>     mapBindIpStnId;//<ip1,stnId1>
	string		strPort;	//
        int             nPort;

	string		str103Port;	//
	int             n103Port;
	_LISTEN_CFG()
	{
                mapBindIpStnId.clear();
		strPort	= "102";
                nPort =102;
		str103Port	= "103";
		n103Port =103;
	}
}LISTEN_CFG;
typedef struct _TMOUT_CFG 
{
	int          nAuthTime;	//
        int          nCommStatCyle;
	_TMOUT_CFG()
	{
		nAuthTime = 30;
                nCommStatCyle = 120;
	}
}TMOUT_CFG;
typedef struct _STN_APCI 
{
    CXJ104APCIHandler *pAPCIHander;
    CLIENT_PRO_INTERFACE  Interface;
    CMessageLog	*pFlowMMSLog;
    CMessageLog	*pFlowLog;
    CMessageLog	*pMessageLog;
    CXJGB103MsgAttachFactory *pMsg104AttachFactory;
    CXJMsgCaster* pMsgCaster;
    
    CHuNTc103ASDUHandler * pFlowASDUHander;
    CXJPro103ClientWay * pStnFlow;
    _STN_APCI()
    {
        pAPCIHander = NULL;
        pFlowMMSLog = NULL;
        pFlowLog = NULL;
        pMessageLog = NULL;
        pMsg104AttachFactory = NULL;
        pMsgCaster = NULL;
        pFlowASDUHander = NULL;
        pStnFlow = NULL;
    }
}STN_APCI;
typedef struct _STN_CFG 
{
    int    nNo;//站序号
    string strStnId;
    char   cStnId[13];
    char   cDragonPath[2048];
    string strMyIP;
    string strStnIP;
    string strStnChnBIP;
    string strStnPort;
    int    nStnPort;
    string strStnPubKey;
    string strMyPrivKey;
    string strMyPubKey;
    STN_APCI  sApciPoiner;
    map<int,string> mapStnDevId;//<No-config.ini,ptid>
    int     nPid; //厂站进程pid
    int     nStnConnectChgSec;
    _STN_CFG()
    {
        nNo = 0;
        bzero(cStnId,sizeof(cStnId));
        bzero(cDragonPath,sizeof(cDragonPath));
        strStnId.clear();
        strMyIP.clear();
        strStnIP.clear();
        strStnChnBIP.clear();
        nStnPort = 2404;
        strStnPort.clear();
        strStnPubKey.clear();
        strMyPrivKey.clear();
        strMyPubKey.clear();
        mapStnDevId.clear();
        nPid = 0;//0代表需要启动，必须0
        nStnConnectChgSec = 0;
    }
    bool operator < (_STN_CFG const &other) const
    {
        return (strcmp(strStnId.c_str(), other.strStnId.c_str()) < 0);
    }
}STN_CFG;
typedef struct _CTL_CFG 
{
	int    nDoSM2;	//
        int    nDoCrcChk;
        int    nUseDb;//1-用 0-不从db，从Ch2PtId.ini
        int    nUseMMS;//1-用 0-不
	_CTL_CFG()
	{
            nDoSM2 = 0;
            nDoCrcChk = 0;
            nUseDb = 0;
            nUseMMS =0;
	}
}CTL_CFG;


class CTaskMngr  
{
    public:  //public函数
/*************************************************************
 函 数 名:   CTaskMngr
 功能概要:   构造函数
 返 回 值:   无
**************************************************************/
	CTaskMngr();
/*************************************************************
 函 数 名:   ~CTaskMngr
 功能概要:	 析构函数
 返 回 值:   无
**************************************************************/
	~CTaskMngr();

/*************************************************************
 函 数 名:   Start
 功能概要:	 初始化服务，启动线程
 返 回 值:   bool true-成功 false-失败
**************************************************************/
	bool Start(bool * pExit);
/*************************************************************
 函 数 名:   End
 功能概要:   停在服务，调用各动态库的停止接口，释放资源
 返 回 值:   bool true-成功 false-失败
**************************************************************/
	bool End();
/*************************************************************
 函 数 名:   End
 功能概要:   停在服务，调用各动态库的停止接口，释放资源
 返 回 值:   bool true-成功 false-失败
**************************************************************/
	bool Free104StnRes(STN_APCI &sStn);

/*************************************************************
 函 数 名：	 主管理线程
 功能概要：   检查故障报告，检查文件，检查厂站模型
 返 回 值:    0-正常 其他-异常
***************************************************************/
        int DoTaskManager(pthread_t ulMyTheadId);
/*************************************************************
 函 数 名：	 主管理线程
 功能概要：   检查故障报告，检查文件，检查厂站模型
 返 回 值:    0-正常 其他-异常
***************************************************************/
        int Do104StnLink(pthread_t ulMyTheadId);
/*************************************************************
函 数 名:   Init_daemon
功能概要:   初始化服务:初始化日志，加载动态库及函数指针
返 回 值:   bool true-成功 false-失败
**************************************************************/
        bool Init_daemon();
/*********************************************************************************
  *Function:		_sendSttp2Msgbus
  *Description:		发送STTP报文到消息中心
  *param[in]:		const STTP_FULL_DATA& pSttpData， STTP报文
  *param[out]:		无
  *Return:			int:0-成功 -1:失败
**********************************************************************************/
	int _sendSttp2Msgbus(const STTP_FULL_DATA& SttpData);
        int ReLoadConfigFile();
        
        void HandleFrontLinkChange(); // lmy add 当前置连接时发送对应子站所有设备连接状态;
		// dragon:songliang 从数据库中读取子站通信状态自动上送周期.
		int __ReadSstnStatusAutoCyl();

		// 服务器在线管理器相关方法
		int InitSrvOnlineManager();             // 初始化服务器在线管理器
		bool GetSrvOnlineMngrConfigFromDB(int& msgLogConfig);    // 从数据库读取服务器在线管理器配置
		int StartStationProcess(const std::string& stationId);  // 启动厂站104连接进程
		int StopStationProcess(const std::string& stationId);   // 停止厂站104连接进程
		void UpdateStationCommStatus(const std::string& stationId, int newStatus); // 更新厂站通信状态
		static int _srv_switch_callback(void* pParam, int pStatus, std::vector<stXJSubstation>& pStationList); // 服务器切换回调
		int OnServerSwitch(int pStatus, std::vector<stXJSubstation>& pStationList); // 处理服务器切换
  private: //private函数 __
/*************************************************************
函 数 名:   Init
功能概要:   初始化服务:初始化日志，加载动态库及函数指针
返 回 值:   bool true-成功 false-失败
**************************************************************/
	bool Init();
        int  PreAPCI();
        int  Pre102();
        int  Pre103();
	void regisAsdu200Tc103(); // lmy add
        void logChange(bool bIsDebug);
        
/*************************************************************
 函 数 名：	  InitLogFile()
 功能概要：   日志文件初始化
 返 回 值:    bool true-成功 false-失败
***************************************************************/
	bool InitLogFile();
        bool GetLogSetFromDB();
        int  GetStnMngrPtIdFromDB();
        bool InitLogFileFromDb();
        bool IsStnMgr(string strPtId,string &strStn);
/*********************************************************************************
  *Function:		LoadConfigFile
  *Description:		读配置文件从ZAS_CHECK_MODEL_CONFIG_INI 和 ZAS_BUS_DEF_CONFIG_INI
  *param[in]:		无
  *param[out]:		无
  *Return:			无
**********************************************************************************/
        int LoadConfigFile();
/*************************************************************
 函 数 名：	  任务管理线程
 功能概要：   每2ms执行一次线程，并打印异常返回
 返 回 值:    线程的this指针
***************************************************************/
	static THREAD_FUNC _TaskMngrThread(void *arg);  
/*************************************************************
 函 数 名：	  任务管理线程
 功能概要：   每2ms执行一次线程，并打印异常返回
 返 回 值:    线程的this指针
***************************************************************/
	static THREAD_FUNC _Task104StnThread(void *arg);  
/*********************************************************************************
  *Function:		__CvtGbk2Utf8
  *Description:		gbk转utf-8
  *param[in]:		gbk char *
  *param[out]:		无
  *Return:			utf-8 char *
**********************************************************************************/
        char * __CvtGbk2Utf8(const char * cObj);
/*********************************************************************************
  *Function:		__CvtUtf82Gbk
  *Description:		utf-8转gbk
  *param[in]:		utf-8 char *
  *param[out]:		无
  *Return:			gbk char *
**********************************************************************************/
        char * __CvtUtf82Gbk(const char * cObj);
/**  @brief         mystrtok分隔字符串用*/ 
    char * myStrtok;
/*********************************************************************************
  *Function:		mystrtok
  *Description:		分隔符函数
  *param[in]:		s-待分隔字符串指针，NULL继续  ct-分隔符
  *param[out]:		无
  *Return:			NULL-无法分隔  非NULL-分隔出来的字符串
**********************************************************************************/
    char * mystrtok(char * s,const char * ct);
/*********************************************************************************
  *Function:		InitCvtLib
  *Description:		加载 字符集编码转换接口库
  *param[in]:		无
  *param[out]:		无
  *Return:			bool, true-成功	false-失败
**********************************************************************************/
        bool InitCvtLib();
/*********************************************************************************
  *Function:		UnInitCvtLib
  *Description:		卸载 字符集编码转换接口库
  *param[in]:		无
  *param[out]:		无
  *Return:			bool, true-成功	false-失败
**********************************************************************************/
        bool UnInitCvtLib(); 
        int PrintBytes(const string &str);
        int PrintBytes(vector<BYTE> &vBytes);
        int PrintBytes(char *message,const int message_len);
        int DecodeMMsType(const string &str);
        int HasLastCOTP(string strStn,int nChNo,string &strMsg);
/*********************************************************************************
  *Function:		init_msg_center_client
  *Description:		初始化消息中心客户端，注册主题和回调函数
  *param[in]:		无
  *param[out]:		无
  *Return:			int, 0:成功	1:失败
**********************************************************************************/
	int init_msg_center_client();
/*************************************************************
 函 数 名:   _fill_APP_NODE_INFO
 功能概要:   填充APP_NODE_INFO结构
 返 回 值:   int, 0:成功，非0：失败
 参    数:   APP_NODE_INFO &node，应用结点基本信息
**************************************************************/
	int _fill_APP_NODE_INFO(APP_NODE_INFO &node);
/*************************************************************
 函 数 名:   _bus_swap_callback
 功能概要:   消息中心回调函数
 返 回 值:   int, 0:成功，非0：失败
 参    数:   void * pRegObj，注册对象
 参    数:   STTP_FULL_DATA& sttp_data，STTP报文
 参    数:   string& strAppNodeName，节点名称
 参    数:   BUS_ADDITION_INFO & addition_info 其它信息
**************************************************************/
	static int _bus_swap_callback(void * pRegObj, STTP_FULL_DATA& sttp_data, string& strAppNodeName, BUS_ADDITION_INFO & addition_info);

    static int _bus_conn_callback(void* pParam, int pStatus, void* pReserved);

    int _BusLinkStatusHandle(int nStatus, void* pReserved);

    void _SttpTypeCatch();    
        
        
    public://public变量  
        
        /** @brief			线程<序号，线程信息>*/
        map<int,THREAD_INFO>    m_map104StnThreadInfo;
        SOCKET		m_102Socket;
        string          m_StnRun;//单厂站运行进程标志和厂站id
        string          m_strCmdName;//命令行传入的进程名
        map<string,STN_CFG>     mapStnLinkCfg;//stnid,info
        
    private: //private变量 
        /** @brief		总线->人机 通道号*/
	short                   m_uSendchnl_up;
	/** @brief		总线->采集前置 通道号*/
	short                   m_uSendchnl_down;
	/** @brief		保信事件通知号*/
	int                     m_CmdCode;
	/** @brief		保信事件通知号*/
	int                     m_RespCode;
        /** @brief		保信事件通知号*/
	int                     m_EventCode;
        /** @brief       从总线接收信息列表锁*/
	CXJLock		m_LockForRcvBusInfList;
        /** @brief                      从总线接收sttp信息的列表*/
	list<BUS_RECV_STTPFULLDATA_INFO>   m_RcvBusInfList; 
        
        
        
        
        

    	/** @brief			 运行退出标志*/
	bool *				 m_pExit;
    
        /** @brief			任务管理线程 线程句柄的指针*/
	THREAD_HANDLE           m_hTheadHandeTaskMngr;
        /** @brief			任务管理线程 线程id的指针*/
	THREAD_ID               m_hTheadIdTaskMngr;

        /** @brief    用于存放xml文件生成过程需要字符串编码转换的结果字符串*/
	char                    m_cChr[MAX_CHAR_BUFF_LEN];
        

        /** @brief			日志配置信息*/
	LOG_INFO			m_LogCfg;      
        /** @brief      数据库TB_COMMU_LOCAL的录波目录*/
        string                  m_strComtradePath;//
        string                  m_strSftpHomePath;//用来去除qt带下来的sftp文件服务器home路径
        string                  m_strScdPath;//自动下载scd文件时，用的子站的映射路径和文件名。不填默认/SCD/station.zip
        
        LISTEN_CFG              sListen61850;
        TMOUT_CFG               sTimeCfg;
        CTL_CFG               sCtlKeyCfg;
        int                   m_workArea;
        int                   m_nModFileCallType;
        
        map<string,string>      m_mapStnMgrId;//子站管理单元的 <ptid,stnid>
        map<string,string>      m_mapStnMgrPt;//子站管理单元的 <stnid,ptid>
        map<string,push* >             map102Push;//<stnid,point>
        map<string,map<string,pthread_t > >         map102PushTheadId;//<stnid,TheadId>

	// lmy add
	map<string,push103* >             map103Push;//<stnid,point>
	map<string,map<string,pthread_t > >         map103PushTheadId;//<stnid,TheadId>


        /** @brief		加载与总线交互库包装类 对象 的指针*/
	CZXLoadBusSwapLib *     m_pLoadBusSwapLib;
        /** @brief          总线交换类 对象 的指针*/
        IZxBusSwap *        m_pIZxBusSwap;

        int m_nLinkStatus;

        /** @brief		数据库访问动态库类 对象 的指针*/
	CXJDBFacade *		m_pDBAcess;

        std::map<std::string ,int > m_mapFntSta; //子站ID，61850前置连接状态 状态（-1：未知，0 未连接，1 已连接)

        // 服务器在线管理器相关成员变量
        CXJSrvOnlineManager* m_pSrvOnlineMngr;              // 服务器在线管理器对象指针
        bool m_bSrvOnlineMngrEnabled;                       // 服务器在线管理器是否启用
    public: //public派生类
	/** @brief          日志记录*/
	CMessageLog *        m_pLogFile;

   
        
    private: //private派生类       
	/** @brief       从总线接收信息列表锁*/

        
    public: //public基类
        
        
    private: //private基类
        /** @brief              字符集转换对象*/
	CZclLibmngr_Gbk2Utf8    m_CharCvtObj;

	/** @brief   上送厂站通信状态的周期值(分钟.) */
	int m_nCylSstnStatus;

	map<int,int> m_mSttpType;
};

#endif /* TASKMNGR_H */

